﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/protocol/codec/helios_codec.h"
#include "mech_communication/protocol/codec/mech_base_codec.h"
#include "mech_communication/protocol/data_struct/helios.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <cstring>

#include "rsfsc_log/rsfsc_log.h"

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

bool HeliosCodec::parseClientPacket(std::vector<uint8_t>& _packet_buffer)
{
  // Check if we have enough data for the frame header
  if (_packet_buffer.size() < sizeof(helios::FrameHead))
  {
    return false;  // Not enough data for a frame header
  }

  while (!_packet_buffer.empty())
  {
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    auto* frame_head = reinterpret_cast<helios::FrameHead*>(_packet_buffer.data());

    // Check if the beginning of the buffer is a valid frame
    if (frame_head->frame_flag == helios::FRAME_FLAG)
    {
      // Check if the complete packet is available
      if (frame_head->length + sizeof(helios::FrameHead) > _packet_buffer.size())
      {
        return true;  // Not enough data for a complete packet
      }
      // Checksum and payload extraction logic...
      const uint16_t CHECK_SUM = helios::checkSum(*frame_head);
      if (CHECK_SUM != frame_head->check_sum)
      {
        _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + sizeof(helios::FrameHead) +
                                                       frame_head->length);  // remove the parsed packet
        error_str = "checkSum error";
        return false;
      }

      const std::vector<uint8_t> PAYLOAD(_packet_buffer.begin() + sizeof(helios::FrameHead),
                                         _packet_buffer.begin() + sizeof(helios::FrameHead) + frame_head->length);
      payload_queue.emplace(frame_head->cmd, PAYLOAD);

      _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + sizeof(helios::FrameHead) +
                                                     frame_head->length);  // remove the parsed packet

      if (_packet_buffer.size() < sizeof(helios::FrameHead))
      {
        break;
      }
      frame_head = static_cast<helios::FrameHead*>(static_cast<void*>(_packet_buffer.data()));
      continue;  // Continue parsing the rest of the buffer
    }

    // If not a valid frame, find the next frame flag
    auto iter = MechBaseCodec::findFrameFlag(_packet_buffer, helios::FRAME_FLAG);
    if (iter != _packet_buffer.end())
    {
      _packet_buffer.erase(_packet_buffer.begin(), iter);
    }
    else
    {
      error_str = "no valid frame found, waiting for more data";
      return false;
    }
  }

  return true;
}

bool HeliosCodec::parseServerPacket(std::vector<uint8_t>& /*_packet_buffer*/)
{
  error_str = "不支持helios server packet的解析";
  return false;
}

bool HeliosCodec::packReadRegister(const std::vector<uint32_t>& _reg_addr,
                                   uint32_t& _expected_packet_response_code,
                                   std::vector<uint8_t>& _packet)
{

  _expected_packet_response_code = helios::NET_CMD_ACK_READ_REGISTER;

  std::vector<uint8_t> payload(_reg_addr.size() * sizeof(uint32_t));

  std::memcpy(payload.data(), _reg_addr.data(), payload.size());

  _packet = packRequest(helios::NET_CMD_READ_REGISTER, payload);

  return true;
}

bool HeliosCodec::packWriteRegister(const std::vector<uint32_t>& _reg_addr,
                                    const std::vector<uint32_t>& _reg_val,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)
{
  if (_reg_addr.size() != _reg_val.size())
  {
    return false;
  }

  std::vector<uint8_t> payload(sizeof(RegisterData) * _reg_addr.size());

  for (size_t i = 0; i < _reg_addr.size(); ++i)
  {
    if (_reg_addr[i] > mech::MAX_REG_ADDR || _reg_addr[i] < mech::MIN_REG_ADDR)
    {
      return false;
    }
    std::memcpy(&payload.at(i * sizeof(RegisterData)), &_reg_addr.at(i), sizeof(uint32_t));
    std::memcpy(&payload.at(i * sizeof(RegisterData) + sizeof(uint32_t)), &_reg_val.at(i), sizeof(uint32_t));
  }

  _expected_packet_response_code = helios::NET_CMD_ACK_WRITE_REGISTER;

  _packet = packRequest(helios::NET_CMD_WRITE_REGISTER, payload);

  return true;
}

bool HeliosCodec::packWriteRegister(const std::vector<RegisterData>& _reg_data_vec,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)
{
  std::vector<uint8_t> payload(sizeof(RegisterData) * _reg_data_vec.size());

  for (size_t i = 0; i < _reg_data_vec.size(); ++i)
  {
    if (_reg_data_vec[i].address > mech::MAX_REG_ADDR || _reg_data_vec[i].address < mech::MIN_REG_ADDR)
    {
      return false;
    }
    std::memcpy(&payload.at(i * sizeof(RegisterData)), &_reg_data_vec.at(i), sizeof(RegisterData));
  }

  _expected_packet_response_code = helios::NET_CMD_ACK_WRITE_REGISTER;

  _packet = packRequest(helios::NET_CMD_WRITE_REGISTER, payload);

  return true;
}

bool HeliosCodec::packWriteTopRegister(const std::vector<uint32_t>& _reg_addr,
                                       const std::vector<uint32_t>& _reg_val,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet)
{
  if (_reg_addr.size() != _reg_val.size())
  {
    return false;
  }

  std::vector<uint8_t> payload(sizeof(RegisterData) * _reg_addr.size());

  for (size_t i = 0; i < _reg_addr.size(); ++i)
  {
    if (_reg_addr[i] > mech::MAX_TOP_REG_ADDR)
    {
      return false;
    }
    std::memcpy(&payload.at(i * sizeof(RegisterData)), &_reg_addr.at(i), sizeof(uint32_t));
    std::memcpy(&payload.at(i * sizeof(RegisterData) + sizeof(uint32_t)), &_reg_val.at(i), sizeof(uint32_t));
  }

  _expected_packet_response_code = helios::NET_CMD_ACK_TOP_WRITE_REGISTER;

  _packet = packRequest(helios::NET_CMD_TOP_WRITE_REGISTER, payload);

  return true;
}

bool HeliosCodec::packWriteTopRegister(const std::vector<RegisterData>& _reg_data,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet)
{
  std::vector<uint8_t> payload(sizeof(RegisterData) * _reg_data.size());

  for (size_t i = 0; i < _reg_data.size(); ++i)
  {
    if (_reg_data[i].address > mech::MAX_TOP_REG_ADDR)
    {
      return false;
    }
    std::memcpy(&payload.at(i * sizeof(RegisterData)), &_reg_data.at(i), sizeof(RegisterData));
  }

  _expected_packet_response_code = helios::NET_CMD_ACK_TOP_WRITE_REGISTER;

  _packet = packRequest(helios::NET_CMD_TOP_WRITE_REGISTER, payload);

  return true;
}

bool HeliosCodec::packReadTopRegister(const std::vector<uint32_t>& _reg_addr,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = helios::NET_CMD_ACK_TOP_READ_REGISTER;

  std::vector<uint8_t> payload(_reg_addr.size() * sizeof(uint32_t));

  std::memcpy(payload.data(), _reg_addr.data(), payload.size());

  _packet = packRequest(helios::NET_CMD_TOP_READ_REGISTER, payload);

  return true;
}
bool HeliosCodec::packReadConRegData(const uint32_t _start_reg_addr,
                                     const uint32_t _reg_num,
                                     uint32_t& _expected_packet_response_code,
                                     std::vector<uint8_t>& _packet)
{
  std::vector<uint32_t> reg_addr(_reg_num);
  for (int i = 0; i < static_cast<int>(_reg_num); ++i)
  {
    reg_addr[i] = _start_reg_addr + 4 * i;
  }
  return packReadRegister(reg_addr, _expected_packet_response_code, _packet);
}
bool HeliosCodec::packReadConTopRegData(const uint32_t _start_reg_addr,
                                        const uint32_t _reg_num,
                                        uint32_t& _expected_packet_response_code,
                                        std::vector<uint8_t>& _packet)
{
  std::vector<uint32_t> reg_addr(_reg_num);
  for (int i = 0; i < static_cast<int>(_reg_num); ++i)
  {
    reg_addr[i] = _start_reg_addr + i;
  }
  return packReadTopRegister(reg_addr, _expected_packet_response_code, _packet);
}

bool HeliosCodec::packWriteConRegData(const uint32_t _start_reg_addr,
                                      const std::vector<uint32_t>& _reg_val,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet)
{
  std::vector<uint32_t> reg_addr(_reg_val.size());
  for (int i = 0; i < static_cast<int>(_reg_val.size()); ++i)
  {
    reg_addr[i] = _start_reg_addr + 4 * i;
  }
  return packWriteRegister(reg_addr, _reg_val, _expected_packet_response_code, _packet);
}
bool HeliosCodec::packWriteConTopRegData(const uint32_t _start_reg_addr,
                                         const std::vector<uint32_t>& _reg_val,
                                         uint32_t& _expected_packet_response_code,
                                         std::vector<uint8_t>& _packet)
{
  std::vector<uint32_t> reg_addr(_reg_val.size());
  for (int i = 0; i < static_cast<int>(_reg_val.size()); ++i)
  {
    reg_addr[i] = _start_reg_addr + i;
  }
  return packWriteTopRegister(reg_addr, _reg_val, _expected_packet_response_code, _packet);
}
bool HeliosCodec::packWriteDigitalRegister(const RegisterData /*_register_data*/,
                                           uint32_t& /*_expected_packet_response_code*/,
                                           std::vector<uint8_t>& /*_packet*/)
{
  error_str = "unimplemented function: packWriteDigitalRegister";
  return false;
}
bool HeliosCodec::packReadDigitalRegister(const uint32_t /*_reg_addr*/,
                                          uint32_t& /*_expected_packet_response_code*/,
                                          std::vector<uint8_t>& /*_packet*/)
{
  error_str = "unimplemented function: packReadDigitalRegister";
  return false;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool HeliosCodec::packGetEyesSafe(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet)
{
  std::vector<uint8_t> data;
  return packReadCmd(helios::NET_CMD_EYES_SAFE, data, _expected_packet_response_code, _packet);
}

bool HeliosCodec::packSetEyesSafe(const uint32_t _is_open,
                                  uint32_t& _expected_packet_response_code,
                                  std::vector<uint8_t>& _packet)
{
  if (_is_open > 1)
  {
    return false;
  }

  _expected_packet_response_code = helios::NET_CMD_ACK_EYES_SAFE;

  std::vector<uint8_t> payload(sizeof(uint32_t));
  std::memcpy(payload.data(), &_is_open, sizeof(uint32_t));

  _packet = packRequest(helios::NET_CMD_EYES_SAFE, payload);

  return true;
}

bool HeliosCodec::packReadConfigPara(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = helios::NET_CMD_ACK_READ_CONFIG;

  std::vector<uint8_t> payload(sizeof(uint32_t));

  _packet = packRequest(helios::NET_CMD_READ_CONFIG, payload);

  return true;
}

bool HeliosCodec::packWriteConfigPara(const helios::ConfigPara& _config_paramater,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = helios::NET_CMD_ACK_WRITE_CONFIG;

  std::vector<uint8_t> payload(sizeof(helios::ConfigPara));

  std::memcpy(payload.data(), &_config_paramater, sizeof(helios::ConfigPara));

  _packet = packRequest(helios::NET_CMD_WRITE_CONFIG, payload);

  return true;
}

bool HeliosCodec::packReadIntensity(const mech::IntensityData& _intensity_data,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = helios::NET_CMD_ACK_TOP_GET_INTENSITY;

  std::vector<uint8_t> payload(sizeof(mech::IntensityData));

  std::memcpy(payload.data(), &_intensity_data, sizeof(mech::IntensityData));

  _packet = packRequest(helios::NET_CMD_TOP_GET_INTENSITY, payload);

  return true;
}
bool HeliosCodec::packCtrlTxChannelExclusively(const int /*_channel_num*/,
                                               const bool /*_is_open*/,
                                               uint32_t& /*_expected_packet_response_code*/,
                                               std::vector<uint8_t>& /*_packet*/)
{
  error_str = "unimplemented function: packTxChannelCtrlExclusively";
  return false;
}
bool HeliosCodec::packReadTxChannelAll(uint32_t& /*_expected_packet_response_code*/, std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("unsupported function: HeliosParser::packReadTxChannelAll");
  return false;
}
bool HeliosCodec::packCtrlTxChannel(const int _channel_num,
                                    const bool _open,
                                    const uint32_t _curr_value,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)
{
  LOG_INDEX_ERROR("unsupported function: HeliosParser::packCtrlTxChannel");
  return false;
}
bool HeliosCodec::packCtrlTxChannelAll(const bool _open,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet)
{
  LOG_INDEX_ERROR("unsupported function: HeliosParser::packCtrlTxChannelAll");
  return false;
}
bool HeliosCodec::getTxChannelRegAddr(const int /*_channel_num*/, uint32_t& /*_tx_channel_reg_addr*/)
{
  LOG_INDEX_ERROR("unsupported function: HeliosParser::getTxChannelRegAddr");
  return false;
}
bool HeliosCodec::packStartWriteTopFlash(const uint32_t /*_start_addr*/,
                                         const uint32_t /*_len*/,
                                         ExpectedResp& /*_expected_packet_response_code*/,
                                         std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("unsupported function: HeliosParser::packRequestWriteTopFlash");
  return false;
}
bool HeliosCodec::packFinishWriteTopFlash(ExpectedResp& /*_expected_packet_response_code*/,
                                          std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("unsupported function: HeliosParser::packFinishWriteTopFlash");
  return false;
}

bool HeliosCodec::isValidMsop(const char* _packet)
{
  const helios::MsopPacket* pkt_data = static_cast<const helios::MsopPacket*>(static_cast<const void*>(_packet));
  return pkt_data->frame_flag == helios::MSOP_FRAME_FLAG;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool HeliosCodec::packReadCmd(const uint32_t _cmd_type,
                              const std::vector<uint8_t>& _data,
                              uint32_t& _expected_packet_response_code,
                              std::vector<uint8_t>& _packet)
{
  if (_cmd_type > helios::NET_CMD_END)
  {
    return false;
  }

  _expected_packet_response_code = _cmd_type;

  std::vector<uint8_t> payload(_data.size());
  std::memcpy(payload.data(), _data.data(), _data.size());

  _packet = packRequest(_cmd_type, payload);

  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool HeliosCodec::packWriteCmd(const uint32_t _cmd_type,
                               const std::vector<uint8_t>& _data,
                               uint32_t& _expected_packet_response_code,
                               std::vector<uint8_t>& _packet)
{
  if (_cmd_type > helios::NET_CMD_END)
  {
    return false;
  }

  _expected_packet_response_code = _cmd_type;

  std::vector<uint8_t> payload(_data.size());
  std::memcpy(payload.data(), _data.data(), _data.size());

  _packet = packRequest(_cmd_type, payload);

  return true;
}

std::vector<uint8_t> HeliosCodec::requestFrameHeadPack(const uint32_t _cmd_type, const uint32_t _payload_size)
{
  std::vector<uint8_t> frame_head_array(sizeof(helios::FrameHead));
  helios::FrameHead frame_head;

  frame_head.frame_flag = helios::FRAME_FLAG;
  frame_head.length     = _payload_size;
  frame_head.cmd        = _cmd_type;
  frame_head.check_sum  = helios::checkSum(frame_head);

  std::memcpy(frame_head_array.data(), &frame_head, sizeof(helios::FrameHead));

  return frame_head_array;
}
std::vector<uint8_t> HeliosCodec::responseFrameHeadPack(const uint32_t _frame_type,
                                                        const mech::ResponseType _response_type,
                                                        const uint32_t _payload_size)
{
  return {};
}

std::vector<uint8_t> HeliosCodec::frameTailPack(std::vector<uint8_t>& _frame_array) { return _frame_array; }

bool HeliosCodec::extractRegister(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() % sizeof(uint32_t) != 0)
  {
    return false;
  }

  if (!register_value_queue.empty())
  {
    std::queue<uint32_t>().swap(register_value_queue);
  }

  uint32_t register_value = 0;
  for (size_t i = 0; i < _payload.size(); i += sizeof(uint32_t))
  {
    memcpy(&register_value, &_payload.at(i), sizeof(uint32_t));
    register_value_queue.emplace(register_value);
  }

  return true;
}

bool HeliosCodec::extractConfigPara(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() != sizeof(helios::ConfigPara))
  {
    return false;
  }

  universal_queue.emplace(_payload);

  return true;
}

bool HeliosCodec::extractEyesSafe(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() != sizeof(uint32_t))
  {
    return false;
  }

  if (!universal_queue.empty())
  {
    std::queue<uint32_t>().swap(register_value_queue);
  }

  uint32_t register_value = 0;
  for (size_t i = 0; i < _payload.size(); i += sizeof(uint32_t))
  {
    memcpy(&register_value, &_payload.at(i), sizeof(uint32_t));
    register_value_queue.emplace(register_value);
  }
  return true;
}

bool HeliosCodec::extractData(const uint32_t _response_code, const std::vector<uint8_t>& _payload)
{
  switch (_response_code)
  {
  case helios::NET_CMD_ACK_TOP_READ_REGISTER:
  case helios::NET_CMD_ACK_READ_REGISTER:
  {
    if (!extractRegister(_payload))
    {
      return false;
    }
    break;
  }
  case helios::NET_CMD_ACK_READ_CONFIG:
  {
    if (_payload.size() != sizeof(helios::ConfigPara))
    {
      return false;
    }
    universal_queue.emplace(_payload);
    break;
  }
  case helios::NET_CMD_ACK_TOP_GET_INTENSITY:
  {
    if (_payload.size() != sizeof(mech::IntensityData))
    {
      return false;
    }
    universal_queue.emplace(_payload);
    break;
  }
  case helios::NET_CMD_ACK_EYES_SAFE:
  {
    return extractEyesSafe(_payload);
    break;
  }

  case helios::NET_CMD_ACK_TOP_WRITE_REGISTER:
  case helios::NET_CMD_ACK_WRITE_REGISTER:
  case helios::NET_CMD_ACK_WRITE_CONFIG: break;

  default: return false;
  }
  return true;
}

}  // namespace lidar
}  // namespace robosense
