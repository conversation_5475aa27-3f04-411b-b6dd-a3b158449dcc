﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "mech_communication/mech_communication.h"
#include "mech_communication/protocol/codec/airy_codec.h"
#include "mech_communication/protocol/codec/airy_crc32_codec.h"
#include "mech_communication/protocol/codec/airy_lite_codec.h"
#include "mech_communication/protocol/codec/bpearl_codec.h"
#include "mech_communication/protocol/codec/helios_codec.h"
#include "mech_communication/protocol/codec/mech_base_codec.h"
#include "mech_communication/protocol/codec/ruby_old_codec.h"
#include "mech_communication/protocol/data_struct/bpearl.h"
#include "mech_communication/protocol/data_struct/helios.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "mech_communication/protocol/data_struct/ruby.h"
#include "mech_communication/register/register_airy.h"
#include "rsfsc_comm_client/rsfsc_serial_client.h"
#include "rsfsc_comm_client/rsfsc_tcp_client.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/asio/deadline_timer.hpp>
#include <boost/asio/use_future.hpp>
#include <boost/bind/bind.hpp>
#include <boost/exception/all.hpp>
#include <boost/system/error_code.hpp>
#include <boost/system/system_error.hpp>
#include <cstdio>  // for popen, pclose, _popen, _pclose
#include <cstdlib>
#include <cstring>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

MechCommunication::MechCommunication(const CommunicationInfo& _comm_info) : log_index_(_comm_info.lidar_index)
{
  init(_comm_info.protocol_type);
  switch (_comm_info.client_type)
  {
  case CommClientType::TCP: rsfsc_comm_client_ptr_ = std::make_unique<RsfscTcpClient>(); break;
  case CommClientType::SERIAL: rsfsc_comm_client_ptr_ = std::make_unique<RsfscSerialClient>(); break;
  default: break;
  }
}
MechCommunication::MechCommunication(const std::string& _project_code, const int _log_index) : log_index_(_log_index)
{
  if (_project_code == "0352")
  {
    init(ProtocolType::MECH_AIRY_LITE);
    rsfsc_comm_client_ptr_ = std::make_unique<RsfscSerialClient>();
  }
  else if (_project_code == "0351")
  {
    init(ProtocolType::MECH_AIRY_CRC32);
    rsfsc_comm_client_ptr_ = std::make_unique<RsfscTcpClient>();
  }
  else
  {
    init(ProtocolType::MECH_AIRY);
    rsfsc_comm_client_ptr_ = std::make_unique<RsfscTcpClient>();
  }
}

CommClientType MechCommunication::getClientType() const
{
  if (dynamic_cast<RsfscTcpClient*>(rsfsc_comm_client_ptr_.get()))
  {
    return CommClientType::TCP;
  }
  else if (dynamic_cast<RsfscSerialClient*>(rsfsc_comm_client_ptr_.get()))
  {
    return CommClientType::SERIAL;
  }
  return CommClientType::UNKNOWN;
}
ProtocolType MechCommunication::getProtocolType() const
{
  if (std::dynamic_pointer_cast<AiryLiteCodec>(ptr_proto_parser_))
  {
    return ProtocolType::MECH_AIRY_LITE;
  }
  else if (std::dynamic_pointer_cast<AiryCrc32Codec>(ptr_proto_parser_))
  {
    return ProtocolType::MECH_AIRY_CRC32;
  }
  else if (std::dynamic_pointer_cast<AiryCodec>(ptr_proto_parser_))
  {
    return ProtocolType::MECH_AIRY;
  }
  else if (std::dynamic_pointer_cast<HeliosCodec>(ptr_proto_parser_))
  {
    return ProtocolType::MECH_HELIOS;
  }
  else if (std::dynamic_pointer_cast<BpearlCodec>(ptr_proto_parser_))
  {
    return ProtocolType::MECH_BPEARL;
  }
  else if (std::dynamic_pointer_cast<RubyOldCodec>(ptr_proto_parser_))
  {
    return ProtocolType::MECH_RUBY_OLD;
  }
  else if (std::dynamic_pointer_cast<MechBaseCodec>(ptr_proto_parser_))
  {
    return ProtocolType::MECH_RUBY;
  }
  return ProtocolType::UNKNOWN;
}

void MechCommunication::setLogIndex(const int _index) { log_index_ = _index; }
int MechCommunication::getLogIndex() const { return log_index_; }

void MechCommunication::init(ProtocolType _protocol_type)
{
  switch (_protocol_type)
  {
  case ProtocolType::MECH_HELIOS:
  {
    ptr_proto_parser_ = std::make_shared<HeliosCodec>();
    break;
  }
  case ProtocolType::MECH_RUBY:
  {
    ptr_proto_parser_ = std::make_shared<MechBaseCodec>();
    break;
  }
  case ProtocolType::MECH_RUBY_OLD:
  {
    ptr_proto_parser_ = std::make_shared<RubyOldCodec>();
    break;
  }
  case ProtocolType::MECH_BPEARL:
  {
    ptr_proto_parser_ = std::make_shared<BpearlCodec>();
    break;
  }
  case ProtocolType::MECH_AIRY_CRC32:
  {
    ptr_proto_parser_ = std::make_shared<AiryCrc32Codec>();
    break;
  }
  case ProtocolType::MECH_AIRY_LITE:
  {
    ptr_proto_parser_ = std::make_shared<AiryLiteCodec>();
    break;
  }
  case ProtocolType::MECH_AIRY:
  {
    ptr_proto_parser_ = std::make_shared<AiryCodec>();
    break;
  }

  default:
    LOG_INDEX_ERROR(msg_header_ + "unsupported protocol type {}!", _protocol_type);
    ptr_proto_parser_ = std::make_shared<AiryCodec>();
    break;
  }
  LOG_INDEX_DEBUG("MechCommunication::MechCommunication -> protocol parser {0} constructed",
                  ptr_proto_parser_->getCurrentProtocolType());
}

MechCommunication::~MechCommunication()
{
  LOG_INDEX_DEBUG("MechCommunication::~MechCommunication -> destructed");
  disconnect();
}

std::string MechCommunication::getCurrentProtocolType()
{
  if (ptr_proto_parser_ == nullptr)
  {
    return "Unknown";
  }
  return ptr_proto_parser_->getCurrentProtocolType();
}

bool MechCommunication::wait(const uint32_t _msec)
{
  std::unique_lock<std::mutex> lock(mutex_);
  cv_.wait_for(lock, std::chrono::milliseconds(_msec), [&] { return is_abort_.load(); });

  return !is_abort_.load();
}

bool MechCommunication::ping(const std::string& _ip, const std::string& _local_address)
{
  if (_ip.empty())
  {
    return false;
  }

#if defined(_WIN32)
  std::string cmd = "ping " + _ip + " -n 1 -l 1 -w 100 > nul ";
  FILE* pipe      = _popen(cmd.c_str(), "r");
#else
  std::string cmd = "ping -c 1 -s 1 -W 0.1 -I " + _local_address + " " + _ip + " > /dev/null 2>&1";
  FILE* pipe      = popen(cmd.c_str(), "r");
#endif

  if (pipe == nullptr)
  {
    return false;
  }

#if defined(_WIN32)
  int ret_code = _pclose(pipe);
  // 在 Windows 上，_pclose 返回的代码已经是子进程的退出代码，因此可以直接使用
  return ret_code == 0;
#else
  int ret_code    = pclose(pipe);
  // 在 Unix 系统上，pclose 返回的值是子进程的退出状态，需要使用 WEXITSTATUS 宏来提取退出码
  return ret_code != -1 && WEXITSTATUS(ret_code) == 0;
#endif
}

bool MechCommunication::pingWait(const std::string& _ip, const uint32_t _msec, const std::string& _local_ip)
{
  // 如果ping失败，且时间超过_msec则return false，超时
  uint32_t time     = 0;
  uint32_t interval = 2000;
  while ((time < _msec) && !is_abort_)
  {
    if (ping(_ip, _local_ip))
    {
      LOG_INDEX_INFO(msg_header_ + "ping success");
      return true;
    }
    LOG_INDEX_DEBUG("ping {} failed, wait {}ms, {}/{}s", _ip, interval, time / interval, _msec / interval);
    std::this_thread::sleep_for(std::chrono::milliseconds(interval));
    time += interval;
  }
  return false;
}

bool MechCommunication::readConfigRegister(const uint32_t _cmd_type,
                                           // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                                           std::vector<uint32_t>& _reg_addr,
                                           std::vector<uint32_t>& _reg_val,
                                           const uint32_t _msec)
{
  std::vector<uint8_t> packet = { 0, 0 };
  std::queue<uint32_t>().swap(ptr_proto_parser_->register_addr_queue);
  std::queue<uint32_t>().swap(ptr_proto_parser_->register_value_queue);

  if (!writeCmd(_cmd_type, packet))
  {
    if (ptr_proto_parser_->error_str != "response type error: 0x3，error type: 数据长度错误")
    {
      return false;
    }
  }

  if (!ptr_proto_parser_->is_config_register_read_finish)
  {
    LOG_INDEX_ERROR("time out, read config register failed, curr register queue size: {0}",
                    ptr_proto_parser_->register_addr_queue.size());
    return false;
  }
  while (!ptr_proto_parser_->register_addr_queue.empty())
  {
    _reg_addr.push_back(ptr_proto_parser_->register_addr_queue.front());
    ptr_proto_parser_->register_addr_queue.pop();
  }
  while (!ptr_proto_parser_->register_value_queue.empty())
  {
    _reg_val.push_back(ptr_proto_parser_->register_value_queue.front());
    ptr_proto_parser_->register_value_queue.pop();
  }
  return true;
}

bool MechCommunication::isValidMsop(const char* _packet) { return ptr_proto_parser_->isValidMsop(_packet); }

bool MechCommunication::setIP(const std::string& _ip)
{
  boost::system::error_code error_code;
  boost::asio::ip::address::from_string(_ip, error_code);
  if (error_code)
  {
    LOG_INDEX_ERROR("invalid ip address: " + _ip + ", err msg: " + error_code.message());
    return false;
  }
  ip_ = _ip;
  return true;
}

std::string MechCommunication::getIP() const { return ip_; }

bool MechCommunication::setPort(const uint16_t _port)
{
  port_ = _port;
  return true;
}

uint16_t MechCommunication::getPort() const { return port_; }

bool MechCommunication::isConnected() const
{
  if (rsfsc_comm_client_ptr_ == nullptr)
  {
    return false;
  }
  return rsfsc_comm_client_ptr_->isConnected();
}

bool MechCommunication::connect(const uint32_t _msec) { return connect(ip_, port_, _msec); }

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::connect(const std::string& _ip, const uint16_t _msop_port, const uint32_t _msec)
{
  if (isConnected())
  {
    disconnect();
  }
  resetAbort();

  msg_header_ = fmt::format("MechCommunication [{}:{}] :: ", _ip, _msop_port);
  if (!pingWait(_ip, _msec))
  {
    LOG_INDEX_ERROR("{} ping failed", msg_header_);
    return false;
  }

  if (!rsfsc_comm_client_ptr_->connect(TcpClientConfig({ _ip, _msop_port, _msec })))
  {
    LOG_INDEX_ERROR("connect failed, ip: " + _ip + ", port: " + std::to_string(_msop_port));
    return false;
  }

  config_para_ = mech::ConfigPara {};
  if (!readConfigParamater(config_para_))
  {
    LOG_INDEX_ERROR("读取config para失败，重试连接");
    disconnect();
    return false;
  }

  LOG_INDEX_INFO(msg_header_ + "读取config para成功, 已完成连接");

  ip_   = _ip;
  port_ = _msop_port;
  return true;
}

bool MechCommunication::connect(const SerialClientConfig& _serial_config, const uint32_t _msec)
{
  if (isConnected())
  {
    disconnect();
  }
  resetAbort();

  msg_header_ = fmt::format("MechCommunication [{}:{}] :: ", _serial_config.port_name, _serial_config.baudrate);

  if (!rsfsc_comm_client_ptr_->connect(_serial_config))
  {
    LOG_INDEX_ERROR("[{}:{}] connect failed", _serial_config.port_name, _serial_config.baudrate);
    return false;
  }

  mech::ConfigPara config_para {};
  if (!readConfigParamater(config_para))
  {
    LOG_INDEX_ERROR("读取config para失败，重试连接");
    disconnect();
    return false;
  }

  LOG_INDEX_INFO(msg_header_ + "读取config para成功, 已完成连接");
  return true;
}

bool MechCommunication::disconnect()
{
  LOG_INDEX_DEBUG(msg_header_ + "call disconnect");
  {
    std::lock_guard<std::mutex> locker(comm_mutex_);
    is_abort_.store(true);
    cv_.notify_all();
  }

  return rsfsc_comm_client_ptr_->disconnect();
}

void MechCommunication::abort()
{
  is_abort_.store(true);
  cv_.notify_all();
}

void MechCommunication::resetAbort()
{
  is_abort_.store(false);
  rsfsc_comm_client_ptr_->resetAbort();
}

void MechCommunication::handleDisconnect(const std::string& _message) { LOG_INDEX_DEBUG(msg_header_ + _message); }

void MechCommunication::handleParseError() { LOG_INDEX_ERROR(msg_header_ + ptr_proto_parser_->error_str); }

bool MechCommunication::checkRegisterAddress(const std::vector<uint32_t>& _reg_addr,
                                             const std::queue<uint32_t>& _register_addr_queue)
{
  if (_register_addr_queue.empty())
  {
    // ignore check
    return true;
  }

  if (_reg_addr.size() != _register_addr_queue.size())
  {
    LOG_INDEX_ERROR(msg_header_ + "return size error! return: " + std::to_string(_register_addr_queue.size()) +
                    ", expect: " + std::to_string(_reg_addr.size()));
    return false;
  }

  return true;
}

void MechCommunication::slotDisconnected() { LOG_INDEX_DEBUG(msg_header_ + "disconnect from host!"); }

bool MechCommunication::waitForTopStartUp(const uint32_t _msec)
{
  mech::ConfigPara config_para {};
  auto start = std::chrono::steady_clock::now();
  while (!is_abort_.load())
  {
    if (!readConfigParamater(config_para, 6000))
    {
      LOG_INDEX_ERROR(msg_header_ + "waitForTopStartUp 读取config参数失败");
      return false;
    }

    if (config_para.getPlVersion() != 0)
    {
      return true;
    }

    if (!wait(2000))
    {
      return false;
    }
    auto elapsed =
      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start).count();
    if (elapsed >= _msec)
    {
      LOG_INDEX_ERROR(msg_header_ + "waitForTopStartUp timeout " + std::to_string(_msec) + "ms");
      return false;
    }
  }
  return false;
}

bool MechCommunication::getIntensityCalibData(mech::IntensityData& _intensity_data, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadIntensity(_intensity_data, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read intensity failed");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  auto payload = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();
  std::memcpy(&_intensity_data, payload.data(), payload.size());

  return true;
}

bool MechCommunication::writeChnAngle(const std::vector<float>& _ver_angle_vec,
                                      const std::vector<float>& _hor_angle_vec,
                                      const uint32_t _msec)
{
  uint16_t angle_num = static_cast<uint16_t>(_ver_angle_vec.size());
  std::vector<uint8_t> data(sizeof(uint16_t) + (angle_num * (sizeof(float) * 2 + sizeof(uint16_t))));
  size_t index = 0;
  MechBaseCodec::copyToPayload(data, angle_num, index);
  for (size_t i = 0; i < angle_num; ++i)
  {
    MechBaseCodec::copyToPayload(data, static_cast<uint16_t>(i + 1), index);
    MechBaseCodec::copyToPayload(data, _ver_angle_vec[i], index);
    MechBaseCodec::copyToPayload(data, _hor_angle_vec[i], index);
  }
  return writeCmd(mech::NET_CMD_OTHER_CHANNEL_ANGLE_WRITE, data, _msec);
}

bool MechCommunication::readChnAngle(std::vector<float>& _ver_angle_vec,
                                     std::vector<float>& _hor_angle_vec,
                                     const uint16_t& _angle_num,
                                     const uint32_t _msec)
{
  std::vector<uint8_t> data(sizeof(uint16_t));
  size_t index = 0;
  MechBaseCodec::copyToPayload(data, _angle_num, index);
  if (!readCmd(mech::NET_CMD_OTHER_CHANNEL_ANGLE_READ, data, _msec))
  {
    LOG_INDEX_ERROR("读取通道角度失败");
    return false;
  }
  if (data.size() != _angle_num * (sizeof(float) * 2 + sizeof(uint16_t)))
  {
    LOG_INDEX_ERROR("通道角度数据长度错误, data size: {}", data.size());
    return false;
  }
  index                  = 0;
  uint16_t chn_num       = 0;
  float angle_vertical   = 0;
  float angle_horizontal = 0;
  std::vector<float> ver_angle_vec;
  std::vector<float> hor_angle_vec;
  for (size_t i = 0; i < _angle_num; ++i)
  {
    MechBaseCodec::copyFromPayload(data, chn_num, index);
    MechBaseCodec::copyFromPayload(data, angle_vertical, index);
    MechBaseCodec::copyFromPayload(data, angle_horizontal, index);
    ver_angle_vec.push_back(angle_vertical);
    hor_angle_vec.push_back(angle_horizontal);
  }
  _ver_angle_vec = ver_angle_vec;
  _hor_angle_vec = hor_angle_vec;
  return true;
}

bool MechCommunication::writeLidarNet(const std::string& _ip,
                                      const uint16_t _msop_port,   // NOLINT(bugprone-easily-swappable-parameters)
                                      const uint16_t _difop_port,  // NOLINT(bugprone-easily-swappable-parameters)
                                      const uint32_t _msec)
{
  boost::system::error_code error_code;
  boost::asio::ip::address_v4 address = boost::asio::ip::make_address_v4(_ip, error_code);
  uint32_t ip_addr                    = address.to_uint();

  if (error_code)
  {
    LOG_INDEX_ERROR("invalid ip address: {}", _ip);
  }

  // C0A8 means 192.168.x.x
  if ((ip_addr >> 16U) != 0xC0A8U)
  {
    LOG_INDEX_ERROR(msg_header_ + "only support 192.168.x.x , not support: " + _ip);
    return false;
  }

  // transfer to big endian
  ip_addr = htonl(ip_addr);

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadConfigPara(expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read config failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (ptr_proto_parser_->universal_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse config error");
    return false;
  }

  packet = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();

  if (!ptr_proto_parser_->packWriteIpPort(ip_addr, _msop_port, _difop_port, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack net para failed!");
    return false;
  }

  if (auto ptr = std::dynamic_pointer_cast<HeliosCodec>(ptr_proto_parser_))
  {
    if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
    {
      return false;
    }
  }
  else
  {
    if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
    {
      if (!wait(1000))
      {
        return false;
      }

      disconnect();

      return connect(_ip, _msop_port, 5000);
    }
  }
  return true;
}

bool MechCommunication::writeWaitResponse(const std::vector<uint8_t>& _packet,
                                          // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                                          const uint32_t _expected_packet_response_code,
                                          const uint32_t _msec)
{
  std::lock_guard<std::mutex> locker(comm_mutex_);
  ptr_proto_parser_->clear();

  auto response = rsfsc_comm_client_ptr_->request(_packet, _msec);
  if (response.empty())
  {
    LOG_INDEX_ERROR("获取的response数据为空");
    return false;
  }

  // 解析接收到的数据
  if (!ptr_proto_parser_->parseClientPacket(response))
  {
    handleParseError();
    return false;
  }

  std::vector<uint32_t> response_codes;
  response_codes = ptr_proto_parser_->handleParsedPayloads();
  if (response_codes.empty() || response_codes.size() != 1)
  {
    LOG_INDEX_ERROR(msg_header_ + "parse packet failed, response_codes: {:#x}", response_codes);
    return false;
  }

  // 检查是否包含预期的响应码
  if (response_codes.at(0) != _expected_packet_response_code)
  {
    LOG_INDEX_ERROR(msg_header_ + "parse packet failed, response_codes: {:#x}, expected_packet_response_code: {:#x}",
                    response_codes.at(0), _expected_packet_response_code);
    return false;
  }

  return true;
}
bool MechCommunication::writeWaitCondition(const std::vector<uint8_t>& _packet, bool& _condition, const uint32_t _msec)
{
  _condition = false;
  if (!rsfsc_comm_client_ptr_->write(_packet))
  {
    LOG_ERROR("发送失败");
    return false;
  }

  auto start = std::chrono::steady_clock::now();
  std::vector<uint8_t> buffer;

  while (true)
  {
    // 检查超时
    auto now = std::chrono::steady_clock::now();
    if (std::chrono::duration_cast<std::chrono::milliseconds>(now - start).count() > _msec)
    {
      LOG_INDEX_WARN("等待响应超时");
      return false;
    }

    auto response = rsfsc_comm_client_ptr_->readSomeTimeout(_msec);
    if (response.empty())
    {
      LOG_INDEX_ERROR("等待响应超时");
      return false;
    }

    // 解析接收到的数据
    if (!ptr_proto_parser_->parseClientPacket(response))
    {
      handleParseError();
      return false;
    }

    auto response_codes = ptr_proto_parser_->handleParsedPayloads();
    if (_condition)
    {
      return true;
    }
  }

  return false;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
template bool MechCommunication::writeCmd<uint8_t>(const uint32_t _cmd_type,
                                                   const uint8_t _value,
                                                   const uint32_t _msec);
template bool MechCommunication::writeCmd<uint16_t>(const uint32_t _cmd_type,
                                                    const uint16_t _value,
                                                    const uint32_t _msec);
template bool MechCommunication::writeCmd<float>(const uint32_t _cmd_type, const float _value, const uint32_t _msec);
template bool MechCommunication::writeCmd<uint32_t>(const uint32_t _cmd_type,
                                                    const uint32_t _value,
                                                    const uint32_t _msec);
template bool MechCommunication::writeCmd<int>(const uint32_t _cmd_type, const int _value, const uint32_t _msec);
template <typename T>
bool MechCommunication::writeCmd(const uint32_t _cmd_type, const T _value, const uint32_t _msec)
{
  std::vector<uint8_t> data(sizeof(T));
  std::memcpy(data.data(), &_value, sizeof(T));
  return writeCmd(_cmd_type, data, _msec);
}
bool MechCommunication::writeCmd(const uint32_t _cmd_type, const std::vector<uint8_t>& _data, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteCmd(_cmd_type, _data, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write cmd failed!");
    return false;
  }

  if (expected_packet_response_code >= mech::NET_CMD_FIRMWARE_UPDATE_APP &&
      expected_packet_response_code <= mech::NET_CMD_FIRMWARE_UPDATE_TOP)
  {
    expected_packet_response_code = mech::NET_CMD_FIRMWARE_UPDATE_ACK;
  }

  if (expected_packet_response_code == mech::NET_CMD_OTHER_FIRM_BOT_READ ||
      expected_packet_response_code == mech::NET_CMD_OTHER_FIRM_TOP_READ ||
      expected_packet_response_code == mech::NET_CMD_OTHER_FIRM_459_READ)
  {
    return writeWaitCondition(packet, ptr_proto_parser_->is_config_register_read_finish, _msec);
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }
  return true;
}

template bool MechCommunication::readCmd<uint8_t>(const uint32_t _cmd_type, uint8_t& _value, const uint32_t _msec);
template bool MechCommunication::readCmd<uint16_t>(const uint32_t _cmd_type, uint16_t& _value, const uint32_t _msec);
template bool MechCommunication::readCmd<float>(const uint32_t _cmd_type, float& _value, const uint32_t _msec);
template bool MechCommunication::readCmd<uint32_t>(const uint32_t _cmd_type, uint32_t& _value, const uint32_t _msec);
template bool MechCommunication::readCmd<int>(const uint32_t _cmd_type, int& _value, const uint32_t _msec);
template <typename T>
bool MechCommunication::readCmd(const uint32_t _cmd_type, T& _value, const uint32_t _msec)
{
  std::vector<uint8_t> data;
  if (!readCmd(_cmd_type, data, _msec))
  {
    return false;
  }
  if (data.size() != sizeof(T))
  {
    LOG_INDEX_ERROR(msg_header_ + "read cmd failed, size error! expect: 2" +
                    ", return: " + MechBaseCodec::hex(static_cast<uint32_t>(data.size())));
    LOG_INDEX_ERROR(msg_header_ + " return data: " + MechBaseCodec::hex(data));
    return false;
  }
  std::memcpy(&_value, data.data(), sizeof(T));
  return true;
}
bool MechCommunication::readCmd(const uint32_t _cmd_type, std::vector<uint8_t>& _data, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadCmd(_cmd_type, _data, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read cmd failed! err_msg: " + ptr_proto_parser_->error_str);
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  {
    std::lock_guard<std::mutex> lock(ptr_proto_parser_->queue_mutex);
    if (ptr_proto_parser_->universal_queue.empty())
    {
      LOG_INDEX_ERROR(msg_header_ + "read cmd failed! err_msg: " + ptr_proto_parser_->error_str);
      return false;
    }
    _data = ptr_proto_parser_->universal_queue.front();
  }

  return true;
}

template bool MechCommunication::readMonitorData<ruby::MonitorData>(ruby::MonitorData& _monitor_data,
                                                                    const uint32_t _msec);
template <typename T>
bool MechCommunication::readMonitorData(T& _monitor_data, const uint32_t _msec)
{
  std::vector<uint8_t> data;
  if (!readCmd(mech::NET_CMD_OTHER_MONITOR_DATA, data, _msec))
  {
    return false;
  }

  if (data.size() != sizeof(T))
  {
    LOG_INDEX_ERROR(msg_header_ + "read monitor data failed, size error! expect: " + std::to_string(sizeof(T)) +
                    ", return: " + MechBaseCodec::hex(static_cast<uint32_t>(data.size())));
    LOG_INDEX_ERROR(msg_header_ + " return data: " + MechBaseCodec::hex(data));
    return false;
  }

  std::memcpy(&_monitor_data, data.data(), sizeof(T));

  return true;
}

bool MechCommunication::writeRegData(const std::vector<uint32_t>& _reg_addr,
                                     const std::vector<uint32_t>& _reg_val,
                                     const uint32_t _msec)
{
  if (_reg_addr.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "_reg_addr cannot be empty");
    return false;
  }

  if (_reg_addr.at(0) < mech::MIN_TOP_REG_ADDR)
  {
    return write459RegData(_reg_addr, _reg_val, _msec);
  }

  if (_reg_addr.at(0) < mech::MIN_REG_ADDR)
  {
    return writeTopRegData(_reg_addr, _reg_val, _msec);
  }

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteRegister(_reg_addr, _reg_val, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write multi register failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return true;
}

bool MechCommunication::writeRegData(const uint32_t _reg_start_addr,
                                     const std::vector<uint32_t>& _reg_val,
                                     const uint32_t _msec)
{
  if (_reg_start_addr < mech::MIN_TOP_REG_ADDR)
  {
    return write459RegData(_reg_start_addr, _reg_val, _msec);
  }
  if (_reg_start_addr < mech::MIN_REG_ADDR)
  {
    return writeTopRegData(_reg_start_addr, _reg_val, _msec);
  }
  std::vector<uint8_t> packet {};
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packWriteConRegData(_reg_start_addr, _reg_val, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write register failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }
  return true;
}

bool MechCommunication::write459RegData(const uint32_t _reg_start_addr,
                                        const std::vector<uint32_t>& _reg_val,
                                        const uint32_t _msec)
{
  for (uint32_t i = 0; i < _reg_val.size(); i++)
  {
    if (!writeRegData(_reg_start_addr + i, _reg_val.at(i), _msec))
    {
      return false;
    }
  }
  return true;
}

bool MechCommunication::writeRegData(const std::vector<RegisterData>& _reg_data_vec, const uint32_t _msec)
{
  if (_reg_data_vec.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "_reg_addr cannot be empty");
    return false;
  }

  if (_reg_data_vec.at(0).address < mech::MIN_REG_ADDR)
  {
    return writeTopRegData(_reg_data_vec, _msec);
  }

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteRegister(_reg_data_vec, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write multi register failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::writeRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec)
{
  if (_reg_addr < mech::MIN_TOP_REG_ADDR)
  {
    return write459RegData(_reg_addr, _reg_val, _msec);
  }
  if (_reg_addr < mech::MIN_REG_ADDR)
  {
    return writeTopRegData(_reg_addr, _reg_val, _msec);
  }

  return writeRegData(_reg_addr, std::vector<uint32_t>({ _reg_val }), _msec);
}

bool MechCommunication::write459RegData(const std::vector<uint32_t>& _reg_addr,
                                        const std::vector<uint32_t>& _reg_val,
                                        const uint32_t _msec)
{
  for (uint32_t i = 0; i < _reg_addr.size(); i++)
  {
    if (!writeRegData(_reg_addr.at(i), _reg_val.at(i), _msec))
    {
      return false;
    }
  }
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::writeTopRegData(const std::vector<uint32_t>& _reg_addr,
                                        const std::vector<uint32_t>& _reg_val,
                                        const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteTopRegister(_reg_addr, _reg_val, expected_packet_response_code, packet))
  {
    std::string reg_str;
    for (const auto& addr : _reg_addr)
    {
      reg_str += ",0x" + MechBaseCodec::hex(addr);
    }
    LOG_INDEX_ERROR(msg_header_ + "pack write top register" + reg_str + " failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return true;
}
bool MechCommunication::writeTopRegData(const uint32_t _reg_start_addr,
                                        const std::vector<uint32_t>& _reg_val,
                                        const uint32_t _msec)
{
  std::vector<uint8_t> packet {};
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packWriteConTopRegData(_reg_start_addr, _reg_val, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write top register failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }
  return true;
}

bool MechCommunication::writeTopRegData(const std::vector<RegisterData>& _reg_data_vec, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteTopRegister(_reg_data_vec, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR("{}pack write top register {}", msg_header_, ptr_proto_parser_->error_str);
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::writeTopRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec)
{
  return writeTopRegData(std::vector<uint32_t>({ _reg_addr }), std::vector<uint32_t>({ _reg_val }), _msec);
}
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::writeTopPairRegData(const uint32_t _reg_addr, const uint16_t _reg_value, const uint32_t _msec)
{
  std::vector<uint32_t> reg_val_vec(
    { static_cast<uint32_t>(_reg_value) >> 8U, static_cast<uint32_t>(_reg_value & 0xffU) });
  return writeTopRegData(_reg_addr, reg_val_vec, _msec);
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::write459RegData(const uint32_t _reg_addr, const uint32_t _reg_value, const uint32_t _msec)
{
  if (!writeTopPairRegData(airy::REG_SENSOR_SPI_ADDR_0, static_cast<uint16_t>(_reg_addr), _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_WDATA, _reg_value, _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_CTRL, 0, _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_CTRL, 1, _msec))
  {
    return false;
  }
  return true;
  // return writeTopRegData({ airy::REG_SENSOR_SPI_ADDR_0, airy::REG_SENSOR_SPI_ADDR_1, airy::REG_SENSOR_SPI_WDATA,
  //                         airy::REG_SENSOR_SPI_CTRL,airy::REG_SENSOR_SPI_CTRL},
  //                       { _reg_addr, _reg_addr, _reg_value, 0, 1 },
  //                       _msec);
}

bool MechCommunication::readTopRegData(const std::vector<uint32_t>& _reg_addr,
                                       std::vector<uint32_t>& _reg_val,
                                       const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadTopRegister(_reg_addr, expected_packet_response_code, packet))
  {
    std::string reg_str;
    for (const auto& addr : _reg_addr)
    {
      reg_str += ",0x" + MechBaseCodec::hex(addr);
    }
    LOG_INDEX_ERROR(msg_header_ + "pack read register " + reg_str + " failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  {
    std::lock_guard<std::mutex> lock(ptr_proto_parser_->queue_mutex);
    while (!ptr_proto_parser_->register_value_queue.empty())
    {
      _reg_val.emplace_back(ptr_proto_parser_->register_value_queue.front());
      ptr_proto_parser_->register_value_queue.pop();
    }
  }

  if (_reg_val.size() != _reg_addr.size())
  {
    LOG_INDEX_ERROR(msg_header_ + "return size error! error_str: " + ptr_proto_parser_->error_str);
    return false;
  }

  return true;
}

bool MechCommunication::read459RegData(const std::vector<uint32_t>& _reg_addr,
                                       std::vector<uint32_t>& _reg_val,
                                       const uint32_t _msec)
{
  _reg_val.clear();
  _reg_val.resize(_reg_addr.size());
  for (uint32_t i = 0; i < _reg_addr.size(); i++)
  {
    if (!read459RegData(_reg_addr.at(i), _reg_val.at(i), _msec))
    {
      return false;
    }
  }
  return true;
}

bool MechCommunication::readTopRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  std::vector<uint32_t> reg_val_vec;
  if (!readTopRegData(_reg_addr, 1, reg_val_vec, _msec))
  {
    return false;
  }

  _reg_val = reg_val_vec.front();
  return true;
}

bool MechCommunication::readTopPairRegData(const uint32_t _reg_addr, uint16_t& _reg_val, const uint32_t _msec)
{
  std::vector<uint32_t> reg_val_vec;
  if (!readTopRegData(_reg_addr, 2, reg_val_vec, _msec))
  {
    return false;
  }

  _reg_val = (reg_val_vec.at(1) + (reg_val_vec.at(0) << 8U)) & 0xffffU;
  return true;
}

bool MechCommunication::read459RegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  if (!writeTopPairRegData(airy::REG_SENSOR_SPI_ADDR_0, static_cast<uint16_t>(_reg_addr), _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_CTRL, 0, _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_CTRL, 2, _msec))
  {
    return false;
  }

  uint32_t reg_val = 0;
  if (!readTopRegData(airy::REG_SENSOR_SPI_RDATA, reg_val, _msec))
  {
    return false;
  }
  _reg_val = reg_val;
  return true;
}

bool MechCommunication::readMixRegData(const std::vector<uint32_t>& _reg_addr,
                                       std::vector<uint32_t>& _reg_val,
                                       const uint32_t _msec)
{
  for (const auto& addr : _reg_addr)
  {
    uint32_t val = 0;
    if (addr > mech::MIN_REG_ADDR && addr < mech::MAX_REG_ADDR)
    {
      if (!readRegData(addr, val, _msec))
      {
        LOG_INDEX_ERROR(msg_header_ + "read register failed!");
        return false;
      }
    }
    else if (addr > mech::MIN_TOP_REG_ADDR && addr < mech::MAX_TOP_REG_ADDR)
    {
      if (!readTopRegData(addr, val, _msec))
      {
        LOG_INDEX_ERROR(msg_header_ + "read register failed!");
        return false;
      }
    }

    if (!wait(10))
    {
      return false;
    }
    _reg_val.emplace_back(val);
  }
  return true;
}

bool MechCommunication::readRegData(const std::vector<uint32_t>& _reg_addr,
                                    std::vector<uint32_t>& _reg_val,
                                    const uint32_t _msec)
{
  if (_reg_addr.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "readRegData _reg_addr cannot be empty");
    return false;
  }
  if (_reg_addr.at(0) < mech::MIN_REG_ADDR)
  {
    return read459RegData(_reg_addr, _reg_val, _msec);
  }

  if (_reg_addr.at(0) < mech::MIN_REG_ADDR)
  {
    return readTopRegData(_reg_addr, _reg_val, _msec);
  }

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadRegister(_reg_addr, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read register failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (!checkRegisterAddress(_reg_addr, ptr_proto_parser_->register_addr_queue))
  {
    std::queue<uint32_t>().swap(ptr_proto_parser_->register_addr_queue);
    std::queue<uint32_t>().swap(ptr_proto_parser_->register_value_queue);
    return false;
  }

  while (!ptr_proto_parser_->register_value_queue.empty())
  {
    _reg_val.emplace_back(ptr_proto_parser_->register_value_queue.front());
    ptr_proto_parser_->register_value_queue.pop();
  }

  if (_reg_val.size() != _reg_addr.size())
  {
    LOG_INDEX_ERROR(msg_header_ + "return size error!" + ptr_proto_parser_->error_str);
    return false;
  }

  return true;
}

bool MechCommunication::readRegData(const uint32_t _start_reg_addr,
                                    const uint32_t _reg_number,
                                    std::vector<uint32_t>& _reg_val,
                                    const uint32_t _msec_10)
{
  if (_start_reg_addr < mech::MIN_TOP_REG_ADDR)
  {
    return read459RegData(_start_reg_addr, _reg_number, _reg_val, _msec_10);
  }
  if (_start_reg_addr < mech::MIN_REG_ADDR)
  {
    return readTopRegData(_start_reg_addr, _reg_number, _reg_val, _msec_10);
  }
  std::vector<uint8_t> packet {};
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packReadConRegData(_start_reg_addr, _reg_number, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read register failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code, _msec_10))
  {
    return false;
  }
  if (ptr_proto_parser_->register_value_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse register value error");
    return false;
  }
  while (!ptr_proto_parser_->register_value_queue.empty())
  {
    _reg_val.emplace_back(ptr_proto_parser_->register_value_queue.front());
    ptr_proto_parser_->register_value_queue.pop();
  }
  return true;
}
bool MechCommunication::readTopRegData(const uint32_t _start_reg_addr,
                                       const uint32_t _reg_number,
                                       std::vector<uint32_t>& _reg_val,
                                       const uint32_t _msec_10)
{
  std::vector<uint8_t> packet {};
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packReadConTopRegData(_start_reg_addr, _reg_number, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read register failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code, _msec_10))
  {
    return false;
  }
  if (ptr_proto_parser_->register_value_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse register value error");
    return false;
  }
  while (!ptr_proto_parser_->register_value_queue.empty())
  {
    _reg_val.emplace_back(ptr_proto_parser_->register_value_queue.front());
    ptr_proto_parser_->register_value_queue.pop();
  }
  return true;
}

bool MechCommunication::read459RegData(const uint32_t _start_reg_addr,
                                       const uint32_t _reg_number,
                                       std::vector<uint32_t>& _reg_val,
                                       const uint32_t _msec_10)
{
  if (_reg_number == 0)
  {
    LOG_INDEX_ERROR(msg_header_ + "read459PairRegData _reg_number cannot be 0");
    return false;
  }
  _reg_val.clear();
  _reg_val.resize(_reg_number);
  for (uint32_t i = 0; i < _reg_number; i++)
  {
    if (!read459RegData(_start_reg_addr + i, _reg_val.at(i), _msec_10))
    {
      return false;
    }
  }
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::startWriteTopFlash(const uint32_t _start_addr, const uint32_t _len, const uint32_t _msec)
{
  std::vector<uint8_t> packet {};
  ExpectedResp expected_resp;

  if (!ptr_proto_parser_->packStartWriteTopFlash(_start_addr, _len, expected_resp, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack start write top flash failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_resp.cmd, _msec))
  {
    return false;
  }

  auto resp_data = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();
  if (resp_data != expected_resp.data)
  {
    LOG_INDEX_ERROR(msg_header_ + "start write top flash failed! response: " + MechBaseCodec::hex(resp_data) +
                    ", expect: " + MechBaseCodec::hex(expected_resp.data));
    return false;
  }
  return true;
}
bool MechCommunication::finishWriteTopFlash(const uint32_t _msec)
{
  std::vector<uint8_t> packet {};
  ExpectedResp expected_resp;

  if (!ptr_proto_parser_->packFinishWriteTopFlash(expected_resp, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack finish write top flash failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_resp.cmd, _msec))
  {
    return false;
  }
  auto resp_data = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();
  if (resp_data != expected_resp.data)
  {
    LOG_INDEX_ERROR(msg_header_ + "finish write top flash failed! response: " + MechBaseCodec::hex(resp_data) +
                    ", expect: " + MechBaseCodec::hex(expected_resp.data));
    return false;
  }
  return true;
}
bool MechCommunication::writeTopFlash(const std::vector<uint8_t>& _data_buffer, uint32_t _addr_start)
{
  uint32_t k_frame_size    = 1024;
  uint32_t k_retry_delay_1 = 100;
  uint32_t k_write_timeout = 8000;

  size_t data_size = _data_buffer.size();

  if (!startWriteTopFlash(_addr_start, data_size, k_write_timeout))
  {
    LOG_INDEX_ERROR("开始写入TopFlash失败");
    return false;
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(k_retry_delay_1));

  if (!writeFlashFrames(_data_buffer, k_frame_size))
  {
    return false;
  }

  if (!finishWriteTopFlash(k_write_timeout) && !finishWriteTopFlash(k_write_timeout))
  {
    LOG_INDEX_ERROR("结束写入TopFlash失败");
    return false;
  }

  LOG_INDEX_INFO("写入TopFlash成功");
  return true;
}

bool MechCommunication::writeFlashFrames(const std::vector<uint8_t>& _data_buffer, uint32_t _frame_size)
{
  uint32_t read_timeout = 8000;
  uint32_t total_size   = _data_buffer.size();

  uint32_t offset    = 0;
  uint32_t pkt_count = 0;

  while (offset < total_size)
  {
    if (is_abort_)
    {
      LOG_INDEX_WARN("写入TopFlash被中断");
      return false;
    }

    ++pkt_count;

    // 计算当前帧大小
    uint32_t frame_size = std::min(_frame_size, total_size - offset);

    std::vector<uint8_t> frame_data(_data_buffer.begin() + offset, _data_buffer.begin() + offset + frame_size);

    // 打包数据
    ExpectedResp expected_resp {};
    std::vector<uint8_t> packet;  // 重用packet缓冲区
    if (!ptr_proto_parser_->packWriteTopFlash(pkt_count, frame_data, expected_resp, packet))
    {
      LOG_INDEX_ERROR("打包TopFlash数据失败: {}", ptr_proto_parser_->error_str);
      return false;
    }

    // 发送并等待响应
    if (!writeWaitResponse(packet, expected_resp.cmd, read_timeout))
    {
      LOG_INDEX_ERROR("写入TopFlash帧{}失败", pkt_count);
      return false;
    }

    offset += frame_size;

    // 每写入32帧打印一次进度
    if (pkt_count % 32 == 0)
    {
      LOG_INDEX_INFO("写入TopFlash进度: {:.1f}%", (offset * 100.0) / total_size);
    }
  }

  LOG_INDEX_INFO("写入TopFlash完成,共{}帧", pkt_count);
  return true;
}

bool MechCommunication::readTopFlash(std::vector<uint8_t>& _data_buffer, uint32_t _addr_start, uint32_t _len)
{
  uint32_t offset       = _addr_start;
  uint32_t read_timeout = 8000;
  uint32_t total_size   = _len;
  uint32_t k_frame_size = 1024;

  while (offset < total_size)
  {
    if (is_abort_)
    {
      LOG_WARN("写入TopFlash被中断");
      return false;
    }

    uint32_t len = std::min(k_frame_size, total_size - offset);

    auto expected_resp = ExpectedResp {};
    std::vector<uint8_t> packet {};
    if (!ptr_proto_parser_->packReadTopFlash(_addr_start + offset, len, expected_resp, packet))
    {
      LOG_INDEX_ERROR("pack read top flash failed! {}", ptr_proto_parser_->error_str);
      return false;
    }
    if (!writeWaitResponse(packet, expected_resp.cmd, read_timeout))
    {
      return false;
    }
    offset += len;
    {
      std::lock_guard<std::mutex> lock(ptr_proto_parser_->queue_mutex);
      _data_buffer.insert(_data_buffer.end(), ptr_proto_parser_->universal_queue.front().begin(),
                          ptr_proto_parser_->universal_queue.front().end());
      ptr_proto_parser_->universal_queue.pop();
    }
  }

  return true;
}

bool MechCommunication::readRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  if (_reg_addr < mech::MIN_TOP_REG_ADDR)
  {
    return read459RegData(_reg_addr, _reg_val, _msec);
  }
  if (_reg_addr < mech::MIN_REG_ADDR)
  {
    return readTopRegData(_reg_addr, _reg_val, _msec);
  }
  std::vector<uint32_t> reg_val_vec;
  if (!readRegData(_reg_addr, 1, reg_val_vec, _msec))
  {
    return false;
  }
  _reg_val = reg_val_vec.front();
  return true;
}

bool MechCommunication::read459PairRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  auto reg_addr_high = _reg_addr + 1;
  auto reg_addr_low  = _reg_addr;

  uint32_t reg_val_high = 0;
  if (!read459RegData(reg_addr_high, reg_val_high, _msec))
  {
    LOG_INDEX_ERROR("读取reg_459寄存器失败");
    return false;
  }
  uint32_t reg_val_low = 0;
  if (!read459RegData(reg_addr_low, reg_val_low, _msec))
  {
    LOG_INDEX_ERROR("读取reg_459寄存器失败");
    return false;
  }
  _reg_val = (reg_val_high << 8U) | reg_val_low;
  LOG_INDEX_INFO("读取reg_459寄存器成功, [{:#x}*2, {:#x}]", _reg_addr, _reg_val);
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::getEyesSafe(uint32_t& _is_open, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packGetEyesSafe(expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack get eyes safe failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (ptr_proto_parser_->register_value_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse eyes safe status error");
    return false;
  }

  _is_open = ptr_proto_parser_->register_value_queue.front();
  ptr_proto_parser_->register_value_queue.pop();

  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechCommunication::setEyesSafe(const uint32_t _is_open, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packSetEyesSafe(_is_open, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack set eyes safe failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return true;
}

bool MechCommunication::writeZeroAngle(const float _angle, const uint32_t _msec)
{
  return writeCmd(mech::NET_CMD_BOTTOM_BOARD_WRITE_ZERO_ANGLE, _angle, _msec);
}
bool MechCommunication::readZeroAngle(float& _angle, const uint32_t _msec)
{
  return readCmd(mech::NET_CMD_BOTTOM_BOARD_READ_ZERO_ANGLE, _angle, _msec);
}

// Explicit template instantiations for the types you want to support
template bool MechCommunication::readConfigParamater<helios::ConfigPara>(helios::ConfigPara& _config_paramater,
                                                                         const uint32_t _msec);
template bool MechCommunication::readConfigParamater<mech::ConfigPara>(mech::ConfigPara& _config_paramater,
                                                                       const uint32_t _msec);
template <typename T>
bool MechCommunication::readConfigParamater(T& _config_paramater, const uint32_t _msec)
{
  if (!(std::is_same<T, helios::ConfigPara>::value || std::is_same<T, mech::ConfigPara>::value))
  {
    LOG_INDEX_ERROR(msg_header_ + "read config parameter, unsupported data type!");
    return false;
  }

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadConfigPara(expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read config parameter failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (ptr_proto_parser_->universal_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse config error");
    return false;
  }

  auto payload = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();

  if (payload.size() != sizeof(T))
  {
    LOG_INDEX_ERROR(msg_header_ + "receive data was not expected! data: " + MechBaseCodec::hex(payload));
    return false;
  }

  std::memcpy(&_config_paramater, payload.data(), sizeof(T));
  if constexpr (std::is_same_v<T, mech::ConfigPara>)
  {
    config_para_ = _config_paramater;
  }
  return true;
}
bool MechCommunication::readConfigParamater(const uint32_t _msec) { return readConfigParamater(config_para_, _msec); }
[[nodiscard]] mech::ConfigPara MechCommunication::getConfigParaCache() const { return config_para_; }

bool MechCommunication::writeConfigParamater(const helios::ConfigPara& _config_paramater, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteConfigPara(_config_paramater, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write config parameter failed");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return true;
}

}  // namespace lidar
}  // namespace robosense