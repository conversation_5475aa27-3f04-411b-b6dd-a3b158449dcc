/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_communication/protocol/codec/airy_codec.h"
#include "mech_communication/protocol/codec/airy_crc32_codec.h"
#include "mech_communication/protocol/codec/mech_base_codec.h"
#include "mech_communication/protocol/data_struct/mech.h"
#include "mech_udp.h"
#include <cstdint>
#include <gtest/gtest.h>

#include "mech_communication/mech_communication.h"
#include <iomanip>
#include <iostream>
#include <vector>

#include "rsfsc_log/rsfsc_log.h"

TEST(NopTest, TestOne) {}

TEST(AiryCrc32CodecTest, CheckSum32Test)
{
  // Test data: ff ff 00 00 0d e1 0b 63 60 61 66 67 64 00 00 00 00 5e fe
  // Expected result: 8c16c040
  // std::vector<uint8_t> test_data = { 0xff, 0xff, 0x00, 0x00, 0x0d, 0xe1, 0x0b, 0x63, 0x60, 0x61,
  //                                    0x66, 0x67, 0x64, 0x00, 0x00, 0x00, 0x00, 0x5e, 0xfe };
  std::vector<uint8_t> test_data = { 0xff, 0xff, 0x00, 0x00, 0x02, 0x20, 0x01, 0xfe };

  uint32_t expected_checksum = 0xd90cb678;
  uint32_t actual_checksum   = robosense::lidar::AiryCrc32Codec::checkSum32(test_data.data(), test_data.size());

  // Print debug information
  // std::cout << "Test data size: " << test_data.size() << std::endl;
  // std::cout << "Expected checksum: 0x" << std::hex << expected_checksum << std::endl;
  // std::cout << "Actual checksum: 0x" << std::hex << actual_checksum << std::endl;

  EXPECT_EQ(expected_checksum, actual_checksum);
}

template <typename T>
bool compareVectors(const std::vector<T>& _expected, const std::vector<T>& _actual)
{
  if (_expected.size() != _actual.size())
  {
    return false;
  }

  auto mismatch_pair = std::mismatch(_expected.begin(), _expected.end(), _actual.begin());
  if (mismatch_pair.first != _expected.end())
  {
    std::cout << "First mismatch at index " << std::distance(_expected.begin(), mismatch_pair.first) << std::endl;
    std::cout << "Expected: " << static_cast<uint32_t>(*mismatch_pair.first)
              << ", Actual: " << static_cast<uint32_t>(*mismatch_pair.second) << std::endl;
    return false;
  }

  return true;
}

// MechUdp启动和停止的压力测试
class MechUdpStressTest : public ::testing::Test
{
protected:
  void SetUp() override
  {
    // 使用一个不太可能被占用的端口
    test_port_   = 6699;
    packet_size_ = 1248;
  }

  void TearDown() override {}

  uint16_t test_port_;
  std::size_t packet_size_;
};

// 测试多次连续启动和停止
TEST_F(MechUdpStressTest, RepeatedStartStopTest)
{
  const int iterations = 100;

  for (int i = 0; i < iterations; i++)
  {
    robosense::lidar::MechUdp udp_client(packet_size_);

    // 启动UDP客户端
    EXPECT_TRUE(udp_client.start("192.168.1.200", test_port_));

    // 短暂等待
    std::this_thread::sleep_for(std::chrono::milliseconds(10));

    // 停止UDP客户端
    EXPECT_TRUE(udp_client.stop());
  }
}

// 测试在多线程环境下启动和停止
TEST_F(MechUdpStressTest, MultiThreadedStartStopTest)
{
  const int thread_count          = 10;
  const int iterations_per_thread = 300;

  std::atomic<bool> start_flag(false);
  std::vector<std::thread> threads;

  // 创建多个线程，每个线程都执行启动和停止操作
  for (int i = 0; i < thread_count; i++)
  {
    threads.emplace_back([this, i, iterations_per_thread, &start_flag]() {
      // 等待所有线程准备就绪
      while (!start_flag.load())
      {
        std::this_thread::yield();
      }

      for (int j = 0; j < iterations_per_thread; j++)
      {
        robosense::lidar::MechUdp udp_client(packet_size_);
        // 使用不同的端口避免冲突
        uint16_t port = test_port_ + i;

        EXPECT_TRUE(udp_client.start("192.168.1.200", port));
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        EXPECT_TRUE(udp_client.stop());
      }
    });
  }

  // 启动所有线程
  start_flag.store(true);

  // 等待所有线程完成
  for (auto& thread : threads)
  {
    if (thread.joinable())
    {
      thread.join();
    }
  }
}

// 测试在高负载下的启动和停止
TEST_F(MechUdpStressTest, HighLoadStartStopTest)
{
  const int iterations = 50;

  for (int i = 0; i < iterations; i++)
  {
    robosense::lidar::MechUdp udp_client(packet_size_);

    // 启动UDP客户端
    EXPECT_TRUE(udp_client.start("192.168.1.200", test_port_));

    // 注册多个回调函数模拟高负载
    for (int j = 0; j < 10; j++)
    {
      udp_client.regRecvCallback([](const char* /*_data*/) {
        // 模拟处理数据的耗时操作
        std::this_thread::sleep_for(std::chrono::microseconds(100));
      });
    }

    // 短暂等待
    std::this_thread::sleep_for(std::chrono::milliseconds(20));

    // 停止UDP客户端
    EXPECT_TRUE(udp_client.stop());
  }
}

// 测试在回调函数中调用stop
TEST_F(MechUdpStressTest, StopFromCallbackTest)
{
  const int iterations = 20;

  for (int i = 0; i < iterations; i++)
  {
    auto udp_client = std::make_shared<robosense::lidar::MechUdp>(packet_size_);
    std::mutex mutex_;
    std::condition_variable cv_;
    bool callback_executed_ = false;

    // 注册回调函数，在回调中调用stop
    udp_client->regRecvCallback([udp_client, &mutex_, &cv_, &callback_executed_](const char* /*_data*/) {
      // 在回调中调用stop
      udp_client->stop();

      // 通知测试线程回调已执行
      {
        std::lock_guard<std::mutex> lock(mutex_);
        callback_executed_ = true;
      }
      cv_.notify_one();
    });

    // 启动UDP客户端
    EXPECT_TRUE(udp_client->start("192.168.1.200", test_port_));

    // 等待一段时间或直到回调执行
    {
      std::unique_lock<std::mutex> lock(mutex_);
      cv_.wait_for(lock, std::chrono::seconds(1), [&callback_executed_] { return callback_executed_; });
    }

    // 确保客户端能够正确停止
    if (!callback_executed_)
    {
      EXPECT_TRUE(udp_client->stop());
    }
  }
}

int main(int _argc, char** _ptr_argv)
{
  testing::InitGoogleTest(&_argc, _ptr_argv);
  return RUN_ALL_TESTS();
}
